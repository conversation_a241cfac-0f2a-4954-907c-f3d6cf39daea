mod snort_classifications;
mod utils_classes;
mod utils_patterns;

use utils_classes::{
    MYSQLValue,
    MYSQLConfig,
    GeoLocationConfig,
    MaliciousConfig,
    DaemonConfig,
    DHCPConfig,
    DNSConfig,
    FilterLogConfig,
    RouterConfig,
    RouterBoardConfig,
    SnortConfig,
};

use dotenv::from_path;
use std::env;
use std::path::Path;


fn main() {
    // load .env
    let env_path = Path::new("../.env");
    from_path(env_path).expect("Failed to load .env");

    let host = env::var("MYSQL_DATADIR").unwrap();
    println!("MYSQL_DATADIR: {}", host);
    // MYSQL_DATADIR: /var/lib/mysql



    println!("\nutils_patterns.rs -------------------------");

    let line = "2023-05-13 09:51:58 Sensor-1 (auth/info) [(squid-1)]";

    if let Some(caps) = utils_patterns::INIT_REG.captures(line) {
        println!("Date: {}", &caps[1]);
        println!("Time: {}", &caps[2]);
        println!("Sensor: {}", &caps[3]);
        // Date: 2023-05-13
        // Time: 09:51:58
        // Sensor: Sensor-1
    }

    if let Some(caps) = utils_patterns::EVENT_REG.captures(line) {
        println!("Event: {}", &caps[1]);
        // Event: (auth/info)
    }

    if let Some(caps) = utils_patterns::ALERT_REG.captures(line) {
        println!("Alert: {}", &caps[1]);
        // Alert: [(squid-1)]
    }



    println!("\nMYSQLConfig -------------------------");

    match MYSQLConfig::DEFAULT_DATA_TYPE.value() {
        MYSQLValue::Str(val) => println!("DEFAULT_DATA_TYPE: {}", val),
        _ => (),
    }
    // DEFAULT_DATA_TYPE: MEDIUMTEXT

    match MYSQLConfig::BUILTIN_DATABASES.value() {
        MYSQLValue::List(dbs) => println!("BUILTIN_DATABASES: {:?}", dbs),
        _ => (),
    }
    // BUILTIN_DATABASES: ["information_schema", "mysql", "performance_schema", "sys"]

    if let MYSQLValue::Str(val) = MYSQLConfig::MYSQL_HOST.value() {
        println!("MYSQL_HOST: {}", val);
    }
    // MYSQL_HOST: localhost

    let in_st = MYSQLConfig::get_infile_statement();
    println!("Infile Statement: {}", in_st);
    // Infile Statement: LOAD DATA LOCAL INFILE



    println!("\nGeoLocationConfig -------------------------");

    // get string
    let title = GeoLocationConfig::TITLE.value();
    if let MYSQLValue::Str(title_str) = title {
        println!("TITLE: {}", title_str);
        // TITLE: GeoLocation
    }

    // get list
    let db_headers__domain = GeoLocationConfig::DB_HEADERS__DOMAIN.value();
    if let MYSQLValue::List(headers) = db_headers__domain {
        println!("DB_HEADERS__DOMAIN: {:?}", headers);
        // DB_HEADERS__DOMAIN: ["ID", "Domain", "Country", ...]
    }

    // get string
    let db_columns_domain = GeoLocationConfig::DB_COLUMNS__DOMAIN.value();
    if let MYSQLValue::Str(columns_str) = db_columns_domain {
        println!("DB_COLUMNS__DOMAIN:\n{}", columns_str);
        // DB_COLUMNS__DOMAIN:
        // ID             INT PRIMARY KEY AUTO_INCREMENT,
        // Domain         MEDIUMTEXT,
        // Country        MEDIUMTEXT,
        // `Country Code` MEDIUMTEXT,
        // ...
    }

    let table_name = GeoLocationConfig::get_table_name("ip");
    println!("Table name (ip): {}", table_name);
    // Table name (ip): geolocationtable__ip

    let logs_parsed_dir = GeoLocationConfig::get_logs_parsed_dir();
    println!("Logs parsed dir: {}", logs_parsed_dir);
    // Logs parsed dir: /FOO/BAR/BAZ/geolocation

    // let select_statement = GeoLocationConfig::get_select_statement("domain");
    // println!("Select statement: {}", select_statement);
    // Select statement: SELECT * FROM geolocationtable__domain



    println!("\nMaliciousConfig -------------------------");

    // get string
    let title = MaliciousConfig::TITLE.value();
    if let MYSQLValue::Str(title_str) = title {
        println!("TITLE: {}", title_str);
        // TITLE: Malicious
    }

    // get list
    let db_headers__domain = MaliciousConfig::DB_HEADERS__DOMAIN.value();
    if let MYSQLValue::List(headers) = db_headers__domain {
        println!("DB_HEADERS__DOMAIN: {:?}", headers);
        // DB_HEADERS__DOMAIN: ["ID", "Domain", "Sources"]
    }

    // get string
    let db_columns_domain = MaliciousConfig::DB_COLUMNS__DOMAIN.value();
    if let MYSQLValue::Str(columns_str) = db_columns_domain {
        println!("DB_COLUMNS__DOMAIN:\n{}", columns_str);
        // DB_COLUMNS__DOMAIN:
        // ID      INT PRIMARY KEY AUTO_INCREMENT,
        // Domain  VARCHAR(5000),
        // Sources MEDIUMTEXT
    }

    let table_name = MaliciousConfig::get_table_name("ip", false);
    println!("Table name (ip): {}", table_name);
    // Table name (ip): malicioustable__ip

    let logs_parsed_dir = MaliciousConfig::get_logs_parsed_dir();
    println!("Logs parsed dir: {}", logs_parsed_dir);
    // Logs parsed dir: /FOO/BAR/BAZ/malicious

    // let select_statement = MaliciousConfig::get_select_statement("domain", false);
    // println!("Select statement: {}", select_statement);
    // Select statement: SELECT * FROM malicioustable__domain



    println!("\nDaemonConfig -------------------------");

    // get string
    let title = DaemonConfig::TITLE.value();
    if let MYSQLValue::Str(title_str) = title {
        println!("TITLE: {}", title_str);
        // TITLE: Daemon
    }

    // get string
    let filterby = DaemonConfig::FILTERBY.value();
    if let MYSQLValue::Str(filterby_str) = filterby {
        println!("FILTERBY: {}", filterby_str);
        // FILTERBY: (daemon/
    }

    // get bool
    let filterby_is_in_alert_type = DaemonConfig::FILTERBY_IS_IN_ALERT_TYPE.value();
    if let MYSQLValue::Bool(filterby_is_in_alert_type_bool) = filterby_is_in_alert_type {
        println!("FILTERBY_IS_IN_ALERT_TYPE: {}", filterby_is_in_alert_type_bool);
        // FILTERBY_IS_IN_ALERT_TYPE: false
    }

    // get list
    let event_types = DaemonConfig::EVENT_TYPES.value();
    if let MYSQLValue::List(headers) = event_types {
        println!("EVENT_TYPES: {:?}", headers);
        // EVENT_TYPES: ["(daemon/alert)", "(daemon/crit)", "(daemon/debug)", ...]
    }

    // get string
    let db_columns = DaemonConfig::DB_COLUMNS.value();
    if let MYSQLValue::Str(columns_str) = db_columns {
        println!("DB_COLUMNS:\n{}", columns_str);
        // DB_COLUMNS:
        // ID           INT PRIMARY KEY AUTO_INCREMENT,
        // Date         VARCHAR(10),
        // Time         VARCHAR(13),
        // Level        MEDIUMTEXT,
        // ...
    }

    let table_name = DaemonConfig::get_table_name();
    println!("Table name: {}", table_name);
    // Table name: daemontable

    let logs_parsed_dir = DaemonConfig::get_logs_parsed_dir();
    println!("Logs parsed dir: {}", logs_parsed_dir);
    // Logs parsed dir: /FOO/BAR/BAZ/daemon

    // let select_statement = DaemonConfig::get_select_statement();
    // println!("Select statement: {}", select_statement);
    // Select statement: SELECT * FROM daemontable



    println!("\nDHCPConfig -------------------------");

    // get string
    let title = DHCPConfig::TITLE.value();
    if let MYSQLValue::Str(title_str) = title {
        println!("TITLE: {}", title_str);
        // TITLE: DHCP
    }

    // get string
    let filter_by = DHCPConfig::FILTERBY.value();
    if let MYSQLValue::Str(filterby_str) = filter_by {
        println!("FILTERBY: {}", filterby_str);
        // FILTERBY: [dhcp]
    }

    // get bool
    let filterby_is_in_alert_type = DHCPConfig::FILTERBY_IS_IN_ALERT_TYPE.value();
    if let MYSQLValue::Bool(filterby_is_in_alert_type_bool) = filterby_is_in_alert_type {
        println!("FILTERBY_IS_IN_ALERT_TYPE: {}", filterby_is_in_alert_type_bool);
        // FILTERBY_IS_IN_ALERT_TYPE: true
    }

    // get list
    let event_types = DHCPConfig::EVENT_TYPES.value();
    if let MYSQLValue::List(headers) = event_types {
        println!("EVENT_TYPES: {:?}", headers);
        // EVENT_TYPES: ["(syslog/info)", "(user/notice)"]
    }

    // get string
    let db_columns = DHCPConfig::DB_COLUMNS.value();
    if let MYSQLValue::Str(columns_str) = db_columns {
        println!("DB_COLUMNS:\n{}", columns_str);
        // DB_COLUMNS:
        // ID           INT PRIMARY KEY AUTO_INCREMENT,
        // Date         VARCHAR(10),
        // Time         VARCHAR(13),
        // Event ID     MEDIUMTEXT,
        // ...
    }

    let table_name = DHCPConfig::get_table_name();
    println!("Table name: {}", table_name);
    // Table name: dhcptable

    let logs_parsed_dir = DHCPConfig::get_logs_parsed_dir();
    println!("Logs parsed dir: {}", logs_parsed_dir);
    // Logs parsed dir: /FOO/BAR/BAZ/dhcp

    // let select_statement = DHCPConfig::get_select_statement();
    // println!("Select statement: {}", select_statement);
    // Select statement: SELECT * FROM dhcptable



    println!("\nDNSConfig -------------------------");

    // get string
    let title = DNSConfig::TITLE.value();
    if let MYSQLValue::Str(title_str) = title {
        println!("TITLE: {}", title_str);
        // TITLE: DNS
    }

    // get string
    let filter_by = DNSConfig::FILTERBY.value();
    if let MYSQLValue::Str(filterby_str) = filter_by {
        println!("FILTERBY: {}", filterby_str);
        // FILTERBY: [dns]
    }

    // get bool
    let filterby_is_in_alert_type = DNSConfig::FILTERBY_IS_IN_ALERT_TYPE.value();
    if let MYSQLValue::Bool(filterby_is_in_alert_type_bool) = filterby_is_in_alert_type {
        println!("FILTERBY_IS_IN_ALERT_TYPE: {}", filterby_is_in_alert_type_bool);
        // FILTERBY_IS_IN_ALERT_TYPE: true
    }

    // get list
    let event_types = DNSConfig::EVENT_TYPES.value();
    if let MYSQLValue::List(headers) = event_types {
        println!("EVENT_TYPES: {:?}", headers);
        // EVENT_TYPES: ["(syslog/info)", "(user/notice)"]
    }

    // get string
    let db_columns = DNSConfig::DB_COLUMNS.value();
    if let MYSQLValue::Str(columns_str) = db_columns {
        println!("DB_COLUMNS:\n{}", columns_str);
        // DB_COLUMNS:
        // ID           INT PRIMARY KEY AUTO_INCREMENT,
        // Date         VARCHAR(10),
        // Time         VARCHAR(13),
        // Thread ID    MEDIUMTEXT,
        // ...
    }

    let table_name = DNSConfig::get_table_name();
    println!("Table name: {}", table_name);
    // Table name: dnstable

    let logs_parsed_dir = DNSConfig::get_logs_parsed_dir();
    println!("Logs parsed dir: {}", logs_parsed_dir);
    // Logs parsed dir: /FOO/BAR/BAZ/dns

    // let select_statement = DNSConfig::get_select_statement();
    // println!("Select statement: {}", select_statement);
    // Select statement: SELECT * FROM dnstable



    println!("\nFilterLogConfig -------------------------");

    // get string
    let title = FilterLogConfig::TITLE.value();
    if let MYSQLValue::Str(title_str) = title {
        println!("TITLE: {}", title_str);
        // TITLE: FilterLog
    }

    // get string
    let filter_by = FilterLogConfig::FILTERBY.value();
    if let MYSQLValue::Str(filterby_str) = filter_by {
        println!("FILTERBY: {}", filterby_str);
        // FILTERBY: [filterlog]
    }

    // get bool
    let filterby_is_in_alert_type = FilterLogConfig::FILTERBY_IS_IN_ALERT_TYPE.value();
    if let MYSQLValue::Bool(filterby_is_in_alert_type_bool) = filterby_is_in_alert_type {
        println!("FILTERBY_IS_IN_ALERT_TYPE: {}", filterby_is_in_alert_type_bool);
        // FILTERBY_IS_IN_ALERT_TYPE: true
    }

    // get list
    let event_types = FilterLogConfig::EVENT_TYPES.value();
    if let MYSQLValue::List(headers) = event_types {
        println!("EVENT_TYPES: {:?}", headers);
        // EVENT_TYPES: []
    }

    // get string
    let db_columns = FilterLogConfig::DB_COLUMNS.value();
    if let MYSQLValue::Str(columns_str) = db_columns {
        println!("DB_COLUMNS:\n{}", columns_str);
        // DB_COLUMNS:
        // ID            INT PRIMARY KEY AUTO_INCREMENT,
        // Date          VARCHAR(10),
        // Time          VARCHAR(13),
        // Protocol Name MEDIUMTEXT,
        // ...
    }

    let table_name = FilterLogConfig::get_table_name();
    println!("Table name: {}", table_name);
    // Table name: filterlogtable

    let logs_parsed_dir = FilterLogConfig::get_logs_parsed_dir();
    println!("Logs parsed dir: {}", logs_parsed_dir);
    // Logs parsed dir: /FOO/BAR/BAZ/filterlog

    // let select_statement = FilterLogConfig::get_select_statement();
    // println!("Select statement: {}", select_statement);
    // Select statement: SELECT * FROM filterlogtable



    println!("\nRouterConfig -------------------------");

    // get string
    let title = RouterConfig::TITLE.value();
    if let MYSQLValue::Str(title_str) = title {
        println!("TITLE: {}", title_str);
        // TITLE: Router
    }

    // get string
    let filter_by = RouterConfig::FILTERBY.value();
    if let MYSQLValue::Str(filterby_str) = filter_by {
        println!("FILTERBY: {}", filterby_str);
        // FILTERBY:
    }

    // get bool
    let filterby_is_in_alert_type = RouterConfig::FILTERBY_IS_IN_ALERT_TYPE.value();
    if let MYSQLValue::Bool(filterby_is_in_alert_type_bool) = filterby_is_in_alert_type {
        println!("FILTERBY_IS_IN_ALERT_TYPE: {}", filterby_is_in_alert_type_bool);
        // FILTERBY_IS_IN_ALERT_TYPE: false
    }

    // get list
    let event_types = RouterConfig::EVENT_TYPES.value();
    if let MYSQLValue::List(headers) = event_types {
        println!("EVENT_TYPES: {:?}", headers);
        // EVENT_TYPES: ["(local7/alert)", "(local7/crit)", "(local7/debug)", ...]
    }

    // get string
    let db_columns = RouterConfig::DB_COLUMNS.value();
    if let MYSQLValue::Str(columns_str) = db_columns {
        println!("DB_COLUMNS:\n{}", columns_str);
        // DB_COLUMNS:
        // ID           INT PRIMARY KEY AUTO_INCREMENT,
        // Date         VARCHAR(10),
        // Time         VARCHAR(13),
        // Level        MEDIUMTEXT,
        // ...
    }

    let table_name = RouterConfig::get_table_name();
    println!("Table name: {}", table_name);
    // Table name: routertable

    let logs_parsed_dir = RouterConfig::get_logs_parsed_dir();
    println!("Logs parsed dir: {}", logs_parsed_dir);
    // Logs parsed dir: /FOO/BAR/BAZ/router

    // let select_statement = RouterConfig::get_select_statement();
    // println!("Select statement: {}", select_statement);
    // Select statement: SELECT * FROM routertable



    println!("\nRouterBoardConfig -------------------------");

    // get string
    let title = RouterBoardConfig::TITLE.value();
    if let MYSQLValue::Str(title_str) = title {
        println!("TITLE: {}", title_str);
        // TITLE: RouterBoard
    }

    // get string
    let filter_by = RouterBoardConfig::FILTERBY.value();
    if let MYSQLValue::Str(filterby_str) = filter_by {
        println!("FILTERBY: {}", filterby_str);
        // FILTERBY:
    }

    // get bool
    let filterby_is_in_alert_type = RouterBoardConfig::FILTERBY_IS_IN_ALERT_TYPE.value();
    if let MYSQLValue::Bool(filterby_is_in_alert_type_bool) = filterby_is_in_alert_type {
        println!("FILTERBY_IS_IN_ALERT_TYPE: {}", filterby_is_in_alert_type_bool);
        // FILTERBY_IS_IN_ALERT_TYPE: false
    }

    // get list
    let event_types = RouterBoardConfig::EVENT_TYPES.value();
    if let MYSQLValue::List(headers) = event_types {
        println!("EVENT_TYPES: {:?}", headers);
        // EVENT_TYPES: ["(local7/alert)", "(local7/crit)", "(local7/debug)", ...]
    }

    // get string
    let db_columns = RouterBoardConfig::DB_COLUMNS.value();
    if let MYSQLValue::Str(columns_str) = db_columns {
        println!("DB_COLUMNS:\n{}", columns_str);
        // DB_COLUMNS:
        // ID           INT PRIMARY KEY AUTO_INCREMENT,
        // Date         VARCHAR(10),
        // Time         VARCHAR(13),
        // Level        MEDIUMTEXT,
        // ...
    }

    let table_name = RouterBoardConfig::get_table_name();
    println!("Table name: {}", table_name);
    // Table name: routerboardtable

    let logs_parsed_dir = RouterBoardConfig::get_logs_parsed_dir();
    println!("Logs parsed dir: {}", logs_parsed_dir);
    // Logs parsed dir: /FOO/BAR/BAZ/routerboard

    // let select_statement = RouterBoardConfig::get_select_statement();
    // println!("Select statement: {}", select_statement);
    // Select statement: SELECT * FROM routerboardtable



    println!("\nSnortConfig -------------------------");

    // get string
    let title = SnortConfig::TITLE.value();
    if let MYSQLValue::Str(title_str) = title {
        println!("TITLE: {}", title_str);
        // TITLE: Snort
    }

    // get string
    let filter_by = SnortConfig::FILTERBY.value();
    if let MYSQLValue::Str(filterby_str) = filter_by {
        println!("FILTERBY: {}", filterby_str);
        // FILTERBY: [snort]
    }

    // get bool
    let filterby_is_in_alert_type = SnortConfig::FILTERBY_IS_IN_ALERT_TYPE.value();
    if let MYSQLValue::Bool(filterby_is_in_alert_type_bool) = filterby_is_in_alert_type {
        println!("FILTERBY_IS_IN_ALERT_TYPE: {}", filterby_is_in_alert_type_bool);
        // FILTERBY_IS_IN_ALERT_TYPE: true
    }

    // get list
    let event_types = SnortConfig::EVENT_TYPES.value();
    if let MYSQLValue::List(headers) = event_types {
        println!("EVENT_TYPES: {:?}", headers);
        // EVENT_TYPES: ["(auth/alert)"]
    }

    // get string
    let db_columns = SnortConfig::DB_COLUMNS.value();
    if let MYSQLValue::Str(columns_str) = db_columns {
        println!("DB_COLUMNS:\n{}", columns_str);
        // DB_COLUMNS:
        // ID           INT PRIMARY KEY AUTO_INCREMENT,
        // Date         VARCHAR(10),
        // Time         VARCHAR(13),
        // `GID:SID`    MEDIUMTEXT,
        // ...
    }

    let table_name = SnortConfig::get_table_name();
    println!("Table name: {}", table_name);
    // Table name: snorttable

    let logs_parsed_dir = SnortConfig::get_logs_parsed_dir();
    println!("Logs parsed dir: {}", logs_parsed_dir);
    // Logs parsed dir: /FOO/BAR/BAZ/snort

    // let select_statement = SnortConfig::get_select_statement();
    // println!("Select statement: {}", select_statement);
    // Select statement: SELECT * FROM snorttable



    // get dict
    let classifications_dict = SnortConfig::CLASSIFICATIONS_DICT.value();
    if let MYSQLValue::Dict(dict) = classifications_dict {
        println!("CLASSIFICATIONS_DICT: {:?}", dict);
        // CLASSIFICATIONS_DICT: {"Executable code was detected": "shellcode-detect,high,1", ...}
    }

    // get list
    let classifications_dict = SnortConfig::CLASSIFICATIONS__CRITICALS.value();
    if let MYSQLValue::List(headers) = classifications_dict {
        println!("CLASSIFICATIONS__CRITICALS: {:?}", headers);
        // CLASSIFICATIONS__CRITICALS: ["Attempted Administrator Privilege Gain", "Attempted User Privilege Gain", ...]
    }



    println!("\n✅ Success")
}
